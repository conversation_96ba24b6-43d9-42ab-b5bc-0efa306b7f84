from datetime import datetime
import json
from sqlalchemy.exc import NoResultFound
from src.shared.data_sources.db_models import Document, DocumentField, DocumentServiceStatus, DocumentFieldValue, \
    DocumentType, \
    NameConvention, CompanyDataSource, CompanyDataDestination, DataSource
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

class DocumentProcessorService:
    def __init__(self, db_client):
        self.db_client = db_client

    def test_connection(self) -> bool:
        return self.db_client.test_connection()

    def get_document_info(self, document_id: int) -> dict:
        with self.db_client.session_scope() as session:
            try:
                document = session.query(Document).filter_by(id=document_id).one()

                #  Parse the metadata JSON safely
                metadata = {}
                if document.document_metadata:
                    try:
                        metadata = json.loads(document.document_metadata)
                    except json.JSONDecodeError as json_err:
                        raise ValueError(f"Invalid JSON in metadata for document {document_id}: {json_err}")

                return {
                    "original_name": document.original_name,
                    "storage_location": document.storage_location,
                    "metadata": metadata,
                    "document_type_id": document.document_type_id
                }

            except NoResultFound:
                raise ValueError(f"Document with ID {document_id} not found")
            except Exception as e:
                raise RuntimeError(f"Error retrieving document info: {e}")

    def get_company_id_by_document_id(self, document_id: int) -> int:
        """
        Retrieves the company_id associated with the given document_id
        by joining the document and company_data_source tables.
        """
        with self.db_client.session_scope() as session:
            try:
                result = (
                    session.query(CompanyDataSource.company_id)
                    .join(Document, CompanyDataSource.id == Document.company_data_source_id)
                    .filter(Document.id == document_id)
                    .one_or_none()
                )

                if result:
                    return result.company_id
                else:
                    raise ValueError(f"No company found for document_id={document_id}")
            except Exception as e:
                raise RuntimeError(f"Error retrieving company_id for document_id={document_id}: {e}")

    def log_document_type_id(self, document_id: int, document_type_id: int) -> None:
        """
        Updates the document_type_id for the given document ID.
        """
        with self.db_client.session_scope() as session:
            try:
                document = session.query(Document).filter_by(id=document_id).one()
                document.document_type_id = document_type_id
                document.updated_at = datetime.utcnow()
                logger.info(f"Updated document_type_id for Document ID {document_id} to {document_type_id}")
            except NoResultFound:
                logger.warning(f"No document found with ID: {document_id}")
            except Exception as e:
                logger.error(f"Error updating document_type_id for Document ID {document_id}: {e}")

    def get_fields_by_document_type_id(self, document_type_id: int) -> list[str]:
        """
        Returns a list of field names associated with the given document_type_id.
        """
        with self.db_client.session_scope() as session:
            try:
                fields = (
                    session.query(DocumentField.field_name)
                    .filter_by(document_type_id=document_type_id)
                    .order_by(DocumentField.id)
                    .all()
                )
                return [f.field_name for f in fields]

            except Exception as e:
                raise RuntimeError(f"Error fetching fields for document_type_id {document_type_id}: {e}")

    def insert_field_value_for_document(self, document_id: int, field_name: str, field_value: str) -> None:
        """
        Inserts an extracted field value into the document_field_values table for a given document and field name.
        """
        with self.db_client.session_scope() as session:
            try:
                # Step 1: Get the document_type_id for this document
                document = session.query(Document).filter_by(id=document_id).one()
                document_type_id = document.document_type_id

                # Step 2: Look up the field by name and document_type_id
                field = session.query(DocumentField).filter_by(
                    document_type_id=document_type_id,
                    field_name=field_name
                ).one()

                # Step 3: Insert into document_field_values
                new_value = DocumentFieldValue(
                    document_id=document_id,
                    document_field_id=field.id,
                    field_value=field_value
                )
                session.add(new_value)
                logger.debug(f"Inserting value for doc {document_id}, field '{field_name}': {field_value}")

            except NoResultFound:
                raise ValueError(f"Field '{field_name}' not found for document_type_id={document_type_id}")
            except Exception as e:
                raise RuntimeError(f"Error inserting field value: {e}")

    def log_document_service_status(
            self,
            document_id: int,
            service_id: int,
            status_id: int,
            metadata: str=None
    ) -> None:
        """
        Logs a new entry into the document_service_status table with the given document_id,
        service_id, status_id, and optional JSON metadata.
        """
        with self.db_client.session_scope() as session:
            try:
                new_entry = DocumentServiceStatus(
                    document_id=document_id,
                    service_id=service_id,
                    status_id=status_id,
                    service_metadata=metadata,
                )
                session.add(new_entry)
                logger.info(
                    f"Logged document service status: document_id={document_id}, "
                    f"service_id={service_id}, status_id={status_id}, metadata={metadata}"
                )
            except Exception as e:
                logger.error(f"Error logging document service status: {e}")

    def get_document_type_id_by_doc_type_name(self, type_name: str) -> int:
        """
        Given a document type name (e.g., 'Affidavit'), returns the corresponding document_type_id.
        Raises ValueError if not found.
        """
        with self.db_client.session_scope() as session:
            try:
                doc_type = (
                    session.query(DocumentType)
                    .filter(DocumentType.type == type_name)
                    .one()
                )
                return doc_type.id
            except NoResultFound:
                raise ValueError(f"Document type '{type_name}' not found.")
            except Exception as e:
                raise RuntimeError(f"Error retrieving document_type_id for '{type_name}': {e}")

    def log_document_name(self, document_id: int, document_name: str) -> None:
        """
        Updates the document_name for the given document ID.
        """
        with self.db_client.session_scope() as session:
            try:
                document = session.query(Document).filter_by(id=document_id).one()
                document.document_name = document_name
                document.updated_at = datetime.utcnow()
                logger.info(f"Set document type id to: {document_name}")
            except NoResultFound:
                logger.warning(f"No document found with ID: {document_id}")
            except Exception as e:
                logger.error(f"Error updating document_name for Document ID {document_id}: {e}")

    def get_naming_template_by_document_type(
            self,
            document_type_id: int
    ) -> str:
        """
        Retrieves the naming template for a specific document_type_id.
        If multiple templates are found, logs a warning and returns the first one.
        """
        with self.db_client.session_scope() as session:
            try:
                templates = (
                    session.query(NameConvention)
                    .filter_by(document_type_id=document_type_id)
                    .all()
                )

                if not templates:
                    raise ValueError(
                        f"No naming template found for document_type_id={document_type_id}"
                    )

                if len(templates) > 1:
                    logger.warning(
                        f"[NAMING] Found {len(templates)} naming templates for "
                        f"document_type_id={document_type_id}. Using the first one."
                    )

                return templates[0].name_template

            except Exception as e:
                raise RuntimeError(
                    f"Error retrieving naming template for document_type_id={document_type_id}: {e}"
                )

    def _get_all_document_types(self):
        with self.db_client.session_scope() as session:
            try:
                document_types = session.query(DocumentType).all()
                doc_types = [doc_type.type for doc_type in document_types]
                return doc_types
            except Exception as e:
                raise RuntimeError(f"Error retrieving all document types: {e}")

    def mark_document_for_manual_review(self, document_id: int) -> None:
        """
        Sets the 'needs_manual_review' flag to True for the specified document.
        """
        with self.db_client.session_scope() as session:
            try:
                document = session.query(Document).filter_by(id=document_id).one_or_none()

                if not document:
                    raise ValueError(f"Document with ID {document_id} not found.")

                document.needs_manual_review = True
                document.updated_at = datetime.utcnow()

                session.add(document)  # Optional, SQLAlchemy will track it, but explicit is fine
                session.commit()

                logger.info(f"Document ID {document_id} marked for manual review.")

            except Exception as e:
                logger.error(f" Failed to mark document {document_id} for manual review: {e}", exc_info=True)
                raise RuntimeError(f"Failed to update document {document_id}: {e}")

    def get_destination_source_id_by_document_id(self, document_id: int) -> int:
        """
        Returns the destination_source_id associated with the given document_id.
        Logs a warning if multiple IDs are found and returns the first one.
        """
        with self.db_client.session_scope() as session:
            try:
                results = (
                    session.query(CompanyDataDestination.destination_source_id)
                    .join(DataSource, DataSource.id == CompanyDataDestination.destination_source_id)
                    .join(CompanyDataSource, CompanyDataSource.data_source_id == DataSource.id)
                    .join(Document, Document.company_data_source_id == CompanyDataSource.id)
                    .filter(Document.id == document_id)
                    .all()
                )

                if not results:
                    raise ValueError(f"No destination_source_id found for document_id={document_id}")

                if len(results) > 1:
                    logger.warning(
                        f"Multiple destination_source_id entries found for doc_id={document_id}; returning the first.")

                return results[0][0]  # return the first destination_source_id

            except Exception as e:
                raise RuntimeError(f"Error retrieving destination_source_id for document_id={document_id}: {e}")