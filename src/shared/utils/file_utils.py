import os
import tempfile
from pathlib import Path
from typing import <PERSON>ple, Union
import logging

logger = logging.getLogger(__name__)

class FileHandler:
    @staticmethod
    def detect_file_type(document: Union[bytes, str]) -> Tuple[str, str]:
        """Detects the file type based on magic bytes or string content."""
        if not document:
            return 'unknown', '.bin'

        if isinstance(document, str):
            return 'text', '.txt'

        if document.startswith(b'%PDF'):
            return 'pdf', '.pdf'
        elif document.startswith(b'\xff\xd8\xff'):
            return 'jpeg', '.jpg'
        elif document.startswith(b'\x89PNG\r\n\x1a\n'):
            return 'png', '.png'
        elif document.startswith(b'PK\x03\x04') and b'word/' in document[:1024]:
            return 'docx', '.docx'
        elif document.startswith(b'PK\x03\x04'):
            return 'zip', '.zip'
        elif document.startswith(b'GIF87a') or document.startswith(b'GIF89a'):
            return 'gif', '.gif'
        elif document.startswith(b'RIFF') and b'WEBP' in document[:12]:
            return 'webp', '.webp'

        logger.warning("Unknown file type detected from magic bytes")
        return 'unknown', '.bin'

    @staticmethod
    def create_temp_file(content: bytes, suffix: str) -> Path:
        """Creates a temp file with the given content and suffix."""
        tmp = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)
        tmp.write(content)
        tmp.flush()
        tmp.close()
        return Path(tmp.name)

    @staticmethod
    def clean_temp_file(file_path: Path):
        """Deletes a temp file if it exists."""
        try:
            if file_path and file_path.exists():
                os.remove(file_path)
                logger.debug(f"Deleted temp file: {file_path}")
        except Exception as err:
            logger.warning(f"Failed to delete temp file {file_path}: {err}")