from pathlib import Path
from typing import Optional
from io import BytesIO
from azure.storage.blob import BlobServiceClient, ContainerClient
from azure.core.exceptions import ResourceNotFoundError
from PyPDF2 import PdfReader, PdfWriter
from docx import Document as DocxDocument
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer

from src.shared.utils.logger_config import setup_logger
from src.shared.utils.retry_config import sync_retry

logger = setup_logger(__name__)

class AzureBlobReader:
    """
    Synchronous reader for Azure Blob Storage documents that retrieves preview content:
      - PDFs: first N pages as new PDF
      - DOC/DOCX: convert to PDF, then extract first N pages
      - Images: raw bytes
    """

    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.blob_service_client: BlobServiceClient = BlobServiceClient.from_connection_string(connection_string)
        self._cached_blob_bytes: Optional[bytes] = None

    def _get_container_client(self, container_name: str) -> ContainerClient:
        return self.blob_service_client.get_container_client(container_name)

    @sync_retry
    def test_connection(self, container_name: str) -> bool:
        try:
            self._get_container_client(container_name).get_container_properties()
            logger.info(f"Successfully connected to container '{container_name}'.")
            return True
        except ResourceNotFoundError:
            logger.error(f"Container '{container_name}' not found.")
            return False
        except Exception as e:
            logger.error(f"Connection test failed: {e}", exc_info=True)
            return False

    @sync_retry
    def blob_exists(self, container_name: str, blob_name: str) -> bool:
        try:
            self._get_container_client(container_name).get_blob_client(blob_name).get_blob_properties()
            return True
        except ResourceNotFoundError:
            return False
        except Exception as e:
            logger.error(f"Error checking blob existence: {e}", exc_info=True)
            return False

    @sync_retry
    def _download_blob_content(self, container_name: str, blob_name: str) -> Optional[bytes]:
        try:
            blob_client = self._get_container_client(container_name).get_blob_client(blob_name)
            content = blob_client.download_blob().readall()
            self._cached_blob_bytes = content
            return content
        except ResourceNotFoundError:
            logger.warning(f"Blob '{blob_name}' not found.")
            return None
        except Exception as e:
            logger.error(f"Failed to download blob '{blob_name}': {e}", exc_info=True)
            raise

    def _convert_doc_to_pdf_bytes(self, content: bytes) -> Optional[bytes]:
        """
        Convert DOCX content to PDF using pure Python libraries.
        Extracts text from DOCX and creates a PDF with that text.
        This is perfect for document classification which only needs text content.
        """
        try:
            # Extract text from DOCX
            doc = DocxDocument(BytesIO(content))
            text_content = []

            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text.strip())

            if not text_content:
                logger.warning("No text content found in DOCX document")
                return None

            # Create PDF from extracted text
            pdf_buffer = BytesIO()
            doc_pdf = SimpleDocTemplate(pdf_buffer, pagesize=letter)
            styles = getSampleStyleSheet()
            story = []

            for text in text_content:
                # Create paragraph with normal style
                para = Paragraph(text, styles['Normal'])
                story.append(para)
                story.append(Spacer(1, 12))  # Add space between paragraphs

            doc_pdf.build(story)
            pdf_bytes = pdf_buffer.getvalue()
            pdf_buffer.close()

            logger.info("Successfully converted DOCX to PDF using pure Python.")
            return pdf_bytes

        except Exception as e:
            logger.error("Failed to convert DOCX to PDF using pure Python", exc_info=True)
            return None

    def _extract_first_n_pages_from_pdf_bytes(self, pdf_bytes: bytes, num_pages: int = 5) -> Optional[bytes]:
        try:
            reader = PdfReader(BytesIO(pdf_bytes))
            writer = PdfWriter()
            for i in range(min(num_pages, len(reader.pages))):
                writer.add_page(reader.pages[i])
            output_stream = BytesIO()
            writer.write(output_stream)
            return output_stream.getvalue()
        except Exception as e:
            logger.error("Failed to extract pages from PDF", exc_info=True)
            return None

    @sync_retry
    def get_document_preview(self, container_name: str, blob_name: str, num_pages: int = 5) -> Optional[bytes]:
        blob_content = self._download_blob_content(container_name, blob_name)
        if not blob_content:
            return None

        ext = Path(blob_name).suffix.lower().lstrip(".")

        if ext == "pdf":
            return self._extract_first_n_pages_from_pdf_bytes(blob_content, num_pages)
        elif ext in ("doc", "docx"):
            pdf_bytes = self._convert_doc_to_pdf_bytes(blob_content)
            return self._extract_first_n_pages_from_pdf_bytes(pdf_bytes, num_pages) if pdf_bytes else None
        elif ext in ("jpg", "jpeg", "png"):
            logger.debug(f"Returning raw image content for '{blob_name}'")
            return blob_content
        else:
            logger.warning(f"Unsupported file type for preview: '{blob_name}'")
            return None

    @sync_retry
    def clear_memory(self):
        self._cached_blob_bytes = None

# reader = AzureBlobReader(connection_string=STORAGE_CONNECTION_STRING)
# container = f"company-{1}"
#
# if reader.test_connection(container):
#     if reader.blob_exists(container, "20250618_110510_Rogs & RFP to Pl - Passenger (Alma Flores).pdf"):
#         data = reader.get_first_n_pages_pdf(container, "20250618_110510_Rogs & RFP to Pl - Passenger (Alma Flores).pdf")
#         print(data)
