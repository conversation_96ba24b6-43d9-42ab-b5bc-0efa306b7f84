"""
Company-specific configuration for project matching.
Contains mappings for different companies and their identifier configurations.
"""

from src.filling_service.project_matching_refactor.extraction.models import FileVineVitals

# Case number synonyms for field extraction
case_number_synonyms = ["Claim Number", "Civil Action Number", "Cause No."]

# Field extraction definitions
client_desc = """
Client Name refers to the individual or organization represented by the sender of the document.
It is typically the party on whose behalf the document is being filed—such as the plaintiff, petitioner, applicant, or, in some cases, the defendant.
When a 'Case Style' is provided (e.g., 'GONZALES, IGNACIO v GUZAMAN, MARIA'), the Client Name is usually the first party listed.
This name should be extracted and treated as the Client Name for matching and identification purposes, regardless of the specific role (plaintiff, defendant, etc.), as long as it aligns with the sender's side in the case.
Client Name is always a person name. If you can't identify it leave it as N/A.
"""

party_names_desc = """
Plaintiff and Defendant names refer to the two main parties involved in a legal case.
- The **Plaintiff** is the party initiating the lawsuit.
- The **Defendant** is the party being sued or accused.

These names are typically found in the case caption at the top of court documents, formatted like:
  “Plaintiff Name vs. Defendant Name”
Or:
  “Plaintiff: [Name]” and “Defendant: [Name]”

Your task is to extract the full names of both parties, as clearly written in the document.
Return the names as a structured pair: e.g. {"plaintiff": "...", "defendant": "..."}.
If either is not found, set its value to null.
"""

# Company data destination to model mapping
DATA_DESTINATION_MODEL_MAP = {
    2: FileVineVitals,
    # add id 1 for Clio
}

# Company ID to identifier description mapping
COMPANY_ID_TO_IDENTIFIER_DESCRIPTION = {
    1: client_desc,
    2: party_names_desc,
    # Add more mappings as needed
}


