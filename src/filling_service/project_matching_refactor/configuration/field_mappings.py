# TODO update ids according to company_data_destination_id

FIELD_MAPPINGS = {
    2: {  # Filevine Vitals
        "incident_date": "Incident Date",
        "incident_type": "Incident Type",
        "project_number": "Project Number",
        "first_primary": "First Primary",
        "lead_attorney": "Lead Attorney",
        "sol_date_only": "SOL",
        "meds_total_balance_due": "Meds Total Balance Due (currency)",
        "phase_name": "Phase Name",
        "project_email_address": "project email address",
        "project_or_client_name": "project name or client name or both",
        # Note: "case_number" is intentionally excluded as it's matched separately
    },
    1: {  # Placeholder for another company
        "event_date": "Date of Event",
        "representative": "Attorney in Charge",
        # Add additional fields for company_id=2 here as needed
    }
}
