from typing import Optional, <PERSON>, Tuple, Set, List, Dict
from metaphone import doublemetaphone

from src.filling_service.project_matching_refactor.configuration.field_mappings import FIELD_MAPPINGS
from src.filling_service.project_matching_refactor.utils.date_utils import normalize_date_for_db
from src.shared.data_sources.db_models import FilevineProject, FilevineField
from src.shared.service_registry import db
from collections import namedtuple
import difflib

MatchResult = namedtuple("MatchResult", ["project_id", "match_type", "matched_name"])

class DocumentProjectMatcher:
    def __init__(self, db_client):
        self.db_client = db_client

    def get_project_id_by_case_number(self, case_number: str) -> Tuple[Union[int, None], Optional[str]]:
        """
        Returns project_id and the matched case number from DB if found.
        """
        with self.db_client.session_scope() as session:
            result = session.query(FilevineField.filevine_project_id, FilevineField.field_value).filter(
                FilevineField.field_name.ilike("case number"),
                FilevineField.field_value == case_number
            ).first()

            if result:
                print(f"[FOUND] Project ID {result[0]} for case number '{case_number}'")
                return result[0], result[1]
            else:
                print(f"[NOT FOUND] No project found for case number '{case_number}'")
                return None, None

    @staticmethod
    def normalize_name(name: str) -> List[str]:
        """Lowercase, remove punctuation and accents, split into tokens."""
        if not name:
            return []
        import unicodedata
        name = unicodedata.normalize("NFKD", name).encode("ASCII", "ignore").decode()
        cleaned = ''.join(c for c in name.lower() if c.isalnum() or c.isspace())
        return cleaned.strip().split()

    @staticmethod
    def get_phonetic_tokens(name: str) -> Set[str]:
        """Generate Metaphone codes for all tokens."""
        tokens = DocumentProjectMatcher.normalize_name(name)
        metaphones = set()
        for token in tokens:
            primary, secondary = doublemetaphone(token)
            if primary:
                metaphones.add(primary)
            if secondary:
                metaphones.add(secondary)
        return metaphones

    @staticmethod
    def are_phonetic_variants(token1: str, token2: str) -> bool:
        """Allow slight spelling differences (like z vs s)."""
        if token1 == token2:
            return True
        ratio = difflib.SequenceMatcher(None, token1, token2).ratio()
        return ratio >= 0.85  # customizable threshold for minor typos

    def match_on_client_name(
            self, client_name: str, company_data_destination_id: int
    ) -> Optional[Union[MatchResult, List[MatchResult]]]:
        input_tokens = self.normalize_name(client_name)
        input_phonetics = self.get_phonetic_tokens(client_name)

        print(f"Normalized client name: {set(input_tokens)}")
        print(f"Phonetic client name: {input_phonetics}")

        if not input_tokens:
            return None

        strong_candidates = []
        weak_candidates = []

        with self.db_client.session_scope() as session:
            projects = session.query(FilevineProject).filter_by(
                company_data_destination_id=company_data_destination_id
            ).all()

            for project in projects:
                db_name = project.client_name
                if not db_name:
                    continue

                db_tokens = self.normalize_name(db_name)
                db_phonetics = self.get_phonetic_tokens(db_name)

                if not db_tokens:
                    continue

                # 1. Exact token match
                if set(input_tokens) == set(db_tokens):
                    print(f"[MATCH] Exact token match: {db_name}")
                    return MatchResult(project.project_id, "exact", db_name)

                # 2. Strong token overlap
                common_tokens = set(input_tokens) & set(db_tokens)
                overlap_ratio = len(common_tokens) / max(len(input_tokens), len(db_tokens))
                if len(common_tokens) >= 2 or overlap_ratio >= 0.75:
                    strong_candidates.append(MatchResult(project.project_id, "strong", db_name))
                    continue

                # 3. Phonetic matching
                if len(input_tokens) == len(db_tokens):
                    close_match = all(self.are_phonetic_variants(t1, t2) for t1, t2 in zip(input_tokens, db_tokens))
                    if close_match:
                        print(f"[MATCH] Phonetic match: {client_name} ≈ {db_name}")
                        strong_candidates.append(MatchResult(project.project_id, "phonetic", db_name))
                        continue

                # 4. Weak match
                if len(common_tokens) == 1:
                    token = next(iter(common_tokens))
                    if len(token) > 3:
                        weak_candidates.append(MatchResult(project.project_id, "weak", db_name))

        if strong_candidates:
            print(f"[INFO] Strong name match candidates: {strong_candidates}")
            return strong_candidates

        if weak_candidates:
            print(f"[WARNING] Weak match candidates for review: {weak_candidates}")
            return weak_candidates

        return None

    def evaluate_secondary_matches_with_candidates(
        self,
        destination_source_id: int,
        unpacked_values: dict,
        candidate_project_ids: List[str]
    ) -> Tuple[Union[str, List[str]], bool, Dict[str, Dict[str, Tuple[str, str]]]]:
        """
        Evaluate candidate projects against secondary fields based on business rules.
        Returns:
        - project_id or candidate list
        - manual_review flag
        - dictionary of matched fields per project: {project_id: {field_name: (extracted_value, db_value)}}
        """
        field_map = FIELD_MAPPINGS.get(destination_source_id, {})
        print(f"\n🔍 Evaluating secondary fields for candidates: {candidate_project_ids}")

        with self.db_client.session_scope() as session:
            # Get all relevant field entries
            fields = (
                session.query(FilevineField)
                .filter(FilevineField.filevine_project_id.in_(candidate_project_ids))
                .all()
            )

            from collections import defaultdict
            project_field_map = defaultdict(dict)
            for field in fields:
                project_field_map[str(field.filevine_project_id)][field.field_name] = field.field_value

            matched_projects = []
            all_matched_fields = {}  # {project_id: {field_name: (extracted_value, db_value)}}

            for project_id, db_fields in project_field_map.items():
                matched_keys = set()
                project_matched_fields = {}

                for extracted_key, extracted_value in unpacked_values.items():
                    if not extracted_value or extracted_value == "N/A":
                        continue

                    expected_field_name = field_map.get(extracted_key)
                    if not expected_field_name:
                        continue

                    db_value = db_fields.get(expected_field_name)
                    if not db_value:
                        continue

                    try:
                        if "date" in extracted_key:
                            extracted_norm = normalize_date_for_db(extracted_value)
                            db_norm = normalize_date_for_db(db_value)
                        else:
                            extracted_norm = extracted_value.lower().strip()
                            db_norm = db_value.lower().strip()
                    except ValueError:
                        continue

                    if extracted_norm == db_norm:
                        matched_keys.add(extracted_key)
                        project_matched_fields[extracted_key] = (str(extracted_value), str(db_value))

                # Store matched fields for this project
                all_matched_fields[project_id] = project_matched_fields

                print(f"📋 Project {project_id} matched on fields: {matched_keys}")
                for field, (ext_val, db_val) in project_matched_fields.items():
                    print(f"   {field}: '{ext_val}' ↔ '{db_val}'")

                # Priority-based match rules
                if len(matched_keys) == len(unpacked_values):
                    print(f"✅ CONFIDENT MATCH: Project {project_id} - All fields matched")
                    return project_id, False, all_matched_fields
                elif {"incident_date", "incident_type"}.issubset(matched_keys):
                    print(f"✅ CONFIDENT MATCH: Project {project_id} - Incident date + type matched")
                    return project_id, False, all_matched_fields
                elif {"incident_date", "lead_attorney"}.issubset(matched_keys):
                    print(f"✅ CONFIDENT MATCH: Project {project_id} - Incident date + attorney matched")
                    return project_id, False, all_matched_fields
                elif "project_email_address" in matched_keys:
                    print(f"✅ CONFIDENT MATCH: Project {project_id} - Email matched")
                    return project_id, False, all_matched_fields
                elif matched_keys:
                    matched_projects.append(project_id)

        # If no confident match
        if matched_projects:
            print(f"⚠️  MANUAL REVIEW NEEDED: Multiple projects with partial matches")
            for proj_id in matched_projects:
                print(f"   Project {proj_id}: {list(all_matched_fields.get(proj_id, {}).keys())}")
            return matched_projects, True, all_matched_fields

        print(f"❌ NO MATCHES: No secondary field matches found")
        return [], True, all_matched_fields

    def match_project(
            self,
            company_data_destination_id: int,
            destination_source_id: int,
            unpacked_values: dict
    ) -> Tuple[Union[str, List[str]], bool, Dict[str, Tuple[str, str]]]:
        """
        Full project matching logic following the specified workflow:
        1. Try case number matching first
        2. Try client name matching
        3. For single strong client match, verify with secondary fields
        4. For multiple client matches, evaluate secondary fields among candidates

        Returns:
            - project_id or list of candidate IDs,
            - manual_review flag,
            - dictionary of all matched fields: {field_name: (extracted_value, db_value)}
        """
        print(f"\n🚀 Starting full project matching for company={company_data_destination_id}")
        all_matched_fields = {}  # Final result dictionary

        # === STEP 1: Try matching on Case Number first (if provided) ===
        case_number = unpacked_values.get("case_number")
        if case_number:
            case_number_str = str(case_number).strip()
            print(f"🔎 STEP 1: Attempting match on Case Number: '{case_number_str}'")

            case_project_id, matched_case_number = self.get_project_id_by_case_number(case_number_str)

            if case_project_id:
                print(f"✅ CASE NUMBER MATCH FOUND → project_id={case_project_id}")
                all_matched_fields["case_number"] = (case_number_str, matched_case_number)
                return str(case_project_id), False, all_matched_fields
            else:
                print(f"❌ No project found for case number: '{case_number_str}'")

        # === STEP 2: Try matching on Client Name ===
        client_name = unpacked_values.get("client_name")
        print(f"🔎 STEP 2: Attempting match on Client Name: '{client_name}'")

        if not client_name:
            print("❌ No client name provided. Manual review required.")
            return [], True, all_matched_fields

        name_match_result = self.match_on_client_name(client_name, company_data_destination_id)

        if not name_match_result:
            print("❌ No client name matches found. Manual review required.")
            return [], True, all_matched_fields

        # === STEP 3: Handle Client Name Match Results ===

        # Single strong match (MatchResult)
        if isinstance(name_match_result, MatchResult):
            print(f"✅ SINGLE STRONG CLIENT NAME MATCH → project_id={name_match_result.project_id}")
            all_matched_fields["client_name"] = (client_name, name_match_result.matched_name)

            # Try to verify this single candidate with secondary identifiers
            print(f"🔍 STEP 3a: Verifying single candidate with secondary fields...")

            project_result, manual_review, secondary_fields = self.evaluate_secondary_matches_with_candidates(
                destination_source_id=destination_source_id,
                unpacked_values=unpacked_values,
                candidate_project_ids=[str(name_match_result.project_id)]
            )

            # Merge secondary matched fields
            if str(name_match_result.project_id) in secondary_fields:
                all_matched_fields.update(secondary_fields[str(name_match_result.project_id)])

            return str(name_match_result.project_id), manual_review, all_matched_fields

        # Multiple candidates (List[MatchResult])
        elif isinstance(name_match_result, list) and name_match_result:
            print(f"🔍 STEP 3b: Multiple client name candidates found ({len(name_match_result)})")
            print(f"   Evaluating secondary fields among candidates...")

            candidate_ids = [str(c.project_id) for c in name_match_result]
            matched_names = [c.matched_name for c in name_match_result]

            # Add client_name matches to all candidates
            for i, candidate in enumerate(name_match_result):
                all_matched_fields[f"client_name_candidate_{candidate.project_id}"] = (client_name, matched_names[i])

            project_result, manual_review, secondary_fields = self.evaluate_secondary_matches_with_candidates(
                destination_source_id=destination_source_id,
                unpacked_values=unpacked_values,
                candidate_project_ids=candidate_ids
            )

            # If we found a confident match among candidates
            if isinstance(project_result, str):
                print(f"✅ CONFIDENT MATCH FOUND among candidates → project_id={project_result}")
                # Clean up candidate fields and add the final client_name match
                final_matched_fields = {"client_name": (client_name, matched_names[candidate_ids.index(project_result)])}
                # Add secondary fields for the winning project
                if project_result in secondary_fields:
                    final_matched_fields.update(secondary_fields[project_result])
                return project_result, manual_review, final_matched_fields

            # Multiple candidates still need manual review
            else:
                print(f"⚠️ MANUAL REVIEW NEEDED: Multiple candidates with partial matches")
                # Merge all secondary fields for all candidates
                for proj_id, fields in secondary_fields.items():
                    for field_name, field_values in fields.items():
                        all_matched_fields[f"{field_name}_project_{proj_id}"] = field_values

                return project_result, manual_review, all_matched_fields

        print("❌ Unexpected result from client name matching. Manual review required.")
        return [], True, all_matched_fields


# project_id = doc_case_matcher.match_on_client_name("Hernadez martinez", 6)
# print(project_id)


# Simulated extracted document fields
# unpacked_values = {
#     "incident_date": "2025-02-21T00:00:00.0000000",
#     "incident_type": "motor vehicle accident",
#     "first_primary": "Evelyn",
#     "lead_attorney": "John Smith",
#     "project_email_address": "<EMAIL>"
# }
#
# # Simulated result from name matching
# candidate_project_ids = ['8127', '8132', '9512']
# company_data_destination_id = 6
#
# matched_project_id_or_list, manual_review = doc_case_matcher.evaluate_secondary_matches_with_candidates(
#     company_data_destination_id=company_data_destination_id,
#     unpacked_values=unpacked_values,
#     candidate_project_ids=candidate_project_ids
# )
#
# print(" Final result:")
# print("Matched project(s):", matched_project_id_or_list)
# print("Manual review needed:", manual_review)

# Test matching on case_id
# doc_case_matcher = DocumentProjectMatcher(db)
# result = doc_case_matcher.get_project_id_by_case_number('**********-25')
# print(f"Result: {result}")