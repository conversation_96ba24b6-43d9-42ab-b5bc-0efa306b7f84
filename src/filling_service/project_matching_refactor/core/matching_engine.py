from typing import Optional, <PERSON>, Tuple, Set, List
from metaphone import doublemetaphone

from src.filling_service.project_matching_refactor.configuration.field_mappings import FIELD_MAPPINGS
from src.filling_service.project_matching_refactor.utils.date_utils import normalize_date_for_db
from src.shared.data_sources.db_models import FilevineProject, <PERSON>vineField
from src.shared.service_registry import db
from collections import namedtuple
import difflib


MatchResult = namedtuple("MatchResult", ["project_id", "match_type", "matched_name"])

class DocumentProjectMatcher:
    def __init__(self, db_client):
        self.db_client = db_client

    def get_project_id_by_case_number(self, case_number: str) -> Union[int, None]:
        with self.db_client.session_scope() as session:
            result = session.query(FilevineField.filevine_project_id).filter(
                FilevineField.field_name.ilike("case number"),
                FilevineField.field_value == case_number
            ).first()

            if result:
                print(f"[FOUND] Project ID {result[0]} for case number '{case_number}'")
                return result[0]
            else:
                print(f"[NOT FOUND] No project found for case number '{case_number}'")
                return None

    @staticmethod
    def normalize_name(name: str) -> List[str]:
        """Lowercase, remove punctuation and accents, split into tokens."""
        if not name:
            return []
        import unicodedata
        name = unicodedata.normalize("NFKD", name).encode("ASCII", "ignore").decode()
        cleaned = ''.join(c for c in name.lower() if c.isalnum() or c.isspace())
        return cleaned.strip().split()

    @staticmethod
    def get_phonetic_tokens(name: str) -> Set[str]:
        """Generate Metaphone codes for all tokens."""
        tokens = DocumentProjectMatcher.normalize_name(name)
        metaphones = set()
        for token in tokens:
            primary, secondary = doublemetaphone(token)
            if primary:
                metaphones.add(primary)
            if secondary:
                metaphones.add(secondary)
        return metaphones

    @staticmethod
    def are_phonetic_variants(token1: str, token2: str) -> bool:
        """Allow slight spelling differences (like z vs s)."""
        if token1 == token2:
            return True
        ratio = difflib.SequenceMatcher(None, token1, token2).ratio()
        return ratio >= 0.85  # customizable threshold for minor typos

    def match_on_client_name(
            self, client_name: str, company_data_destination_id: int
    ) -> Optional[Union[MatchResult, List[MatchResult]]]:
        input_tokens = self.normalize_name(client_name)
        input_phonetics = self.get_phonetic_tokens(client_name)

        print(f"Normalized client name: {set(input_tokens)}")
        print(f"Phonetic client name: {input_phonetics}")

        if not input_tokens:
            return None

        strong_candidates = []
        weak_candidates = []

        with self.db_client.session_scope() as session:
            projects = session.query(FilevineProject).filter_by(
                company_data_destination_id=company_data_destination_id
            ).all()

            for project in projects:
                db_name = project.client_name
                if not db_name:
                    continue

                db_tokens = self.normalize_name(db_name)
                db_phonetics = self.get_phonetic_tokens(db_name)

                if not db_tokens:
                    continue

                # 1. Exact token match
                if set(input_tokens) == set(db_tokens):
                    print(f"[MATCH] Exact token match: {db_name}")
                    return MatchResult(project.project_id, "exact", db_name)

                # 2. Strong token overlap
                common_tokens = set(input_tokens) & set(db_tokens)
                overlap_ratio = len(common_tokens) / max(len(input_tokens), len(db_tokens))
                if len(common_tokens) >= 2 or overlap_ratio >= 0.75:
                    strong_candidates.append(MatchResult(project.project_id, "strong", db_name))
                    continue

                # 3. Phonetic matching
                if len(input_tokens) == len(db_tokens):
                    close_match = all(self.are_phonetic_variants(t1, t2) for t1, t2 in zip(input_tokens, db_tokens))
                    if close_match:
                        print(f"[MATCH] Phonetic match: {client_name} ≈ {db_name}")
                        strong_candidates.append(MatchResult(project.project_id, "phonetic", db_name))
                        continue

                # 4. Weak match
                if len(common_tokens) == 1:
                    token = next(iter(common_tokens))
                    if len(token) > 3:
                        weak_candidates.append(MatchResult(project.project_id, "weak", db_name))

        if strong_candidates:
            print(f"[INFO] Strong name match candidates: {strong_candidates}")
            return strong_candidates

        if weak_candidates:
            print(f"[WARNING] Weak match candidates for review: {weak_candidates}")
            return weak_candidates

        return None

    def evaluate_secondary_matches_with_candidates(
        self,
        destination_source_id: int,
        unpacked_values: dict,
        candidate_project_ids: List[str]
    ) -> Tuple[Union[str, List[str]], bool]:
        """
        Evaluate candidate projects against secondary fields based on business rules.
        Returns either a confident project_id or candidate list with manual_review=True.
        """
        field_map = FIELD_MAPPINGS.get(destination_source_id, {})
        print(f"\n Evaluating secondary fields for candidates: {candidate_project_ids}")

        with self.db_client.session_scope() as session:
            # Get all relevant field entries
            fields = (
                session.query(FilevineField)
                .filter(FilevineField.filevine_project_id.in_(candidate_project_ids))
                .all()
            )

            from collections import defaultdict
            project_field_map = defaultdict(dict)
            for field in fields:
                project_field_map[str(field.filevine_project_id)][field.field_name] = field.field_value

            matched_projects = []

            for project_id, db_fields in project_field_map.items():
                matched_keys = set()

                for extracted_key, extracted_value in unpacked_values.items():
                    if not extracted_value or extracted_value == "N/A":
                        continue

                    expected_field_name = field_map.get(extracted_key)
                    if not expected_field_name:
                        continue

                    db_value = db_fields.get(expected_field_name)
                    if not db_value:
                        continue

                    try:
                        if "date" in extracted_key:
                            extracted_norm = normalize_date_for_db(extracted_value)
                            db_norm = normalize_date_for_db(db_value)
                        else:
                            extracted_norm = extracted_value.lower().strip()
                            db_norm = db_value.lower().strip()
                    except ValueError:
                        continue

                    if extracted_norm == db_norm:
                        matched_keys.add(extracted_key)

                print(f" Project {project_id} matched on fields: {matched_keys}")

                # Priority-based match rules
                if len(matched_keys) == len(unpacked_values):
                    return project_id, False
                elif {"incident_date", "incident_type"}.issubset(matched_keys):
                    return project_id, False
                elif {"incident_date", "lead_attorney"}.issubset(matched_keys):
                    return project_id, False
                elif "project_email_address" in matched_keys:
                    return project_id, False
                elif matched_keys:
                    matched_projects.append(project_id)

        # If no confident match
        if matched_projects:
            return matched_projects, True
        return [], True

    def match_project(
            self,
            company_data_destination_id: int,

            unpacked_values: dict
    ) -> Tuple[Union[str, List[str]], bool, Optional[str], Optional[Union[str, List[str]]]]:
        """
        Full project matching logic:
        Returns:
            - project_id or list of candidate IDs,
            - manual_review flag,
            - extracted client/case identifier,
            - matched identifier(s) from DB
        """
        logger.info(f"\n Starting full project matching")

        # === STEP 1: Match on Case Number (if provided) ===
        case_number = unpacked_values.get("case_number")
        if case_number:
            case_number_str = str(case_number).strip()
            print(f"🔎 Attempting match on Case Number: {case_number_str}")
            case_project_id = self.get_project_id_by_case_number(case_number_str)

            if case_project_id:
                print(f" Case Number match found → project_id={case_project_id}")
                return case_project_id, False, case_number_str, case_number_str
            else:
                print(f" No project found for case number: {case_number_str}")

        # === STEP 2: Match on Client Name (fallback) ===
        client_name = unpacked_values.get("client_name")
        print(f" Extracted client name: {client_name}")

        if client_name:
            name_match_result = self.match_on_client_name(client_name, company_data_destination_id)

            # Exact match (MatchResult)
            if isinstance(name_match_result, MatchResult):
                print(f" Exact client name match → project_id={name_match_result.project_id}")
                return name_match_result.project_id, False, client_name, name_match_result.matched_name

            # Multiple candidates
            elif isinstance(name_match_result, list) and name_match_result:
                print(f" Evaluating secondary fields for {len(name_match_result)} name-matched candidates...")
                candidate_ids = [c.project_id for c in name_match_result]
                matched_names = [c.matched_name for c in name_match_result]

                project_result, manual_review = self.evaluate_secondary_matches_with_candidates(
                    company_data_destination_id=company_data_destination_id,
                    unpacked_values=unpacked_values,
                    candidate_project_ids=candidate_ids
                )

                if isinstance(project_result, str):
                    matched_name = matched_names[candidate_ids.index(project_result)]
                    return project_result, manual_review, client_name, matched_name
                else:
                    return project_result, manual_review, client_name, matched_names

        print(" No case_number or client_name match found. Manual review required.")
        return [], True, client_name or case_number, None


# project_id = doc_case_matcher.match_on_client_name("Hernadez martinez", 6)
# print(project_id)


# Simulated extracted document fields
# unpacked_values = {
#     "incident_date": "2025-02-21T00:00:00.0000000",
#     "incident_type": "motor vehicle accident",
#     "first_primary": "Evelyn",
#     "lead_attorney": "John Smith",
#     "project_email_address": "<EMAIL>"
# }
#
# # Simulated result from name matching
# candidate_project_ids = ['8127', '8132', '9512']
# company_data_destination_id = 6
#
# matched_project_id_or_list, manual_review = doc_case_matcher.evaluate_secondary_matches_with_candidates(
#     company_data_destination_id=company_data_destination_id,
#     unpacked_values=unpacked_values,
#     candidate_project_ids=candidate_project_ids
# )
#
# print(" Final result:")
# print("Matched project(s):", matched_project_id_or_list)
# print("Manual review needed:", manual_review)

# Test matching on case_id
# doc_case_matcher = DocumentProjectMatcher(db)
# result = doc_case_matcher.get_project_id_by_case_number('**********-25')
# print(f"Result: {result}")