import json
import os
import re
import tempfile
from pathlib import Path
from typing import Optional, Tuple, List, Type, Dict, Union
from src.filling_service.prompts.name_generation_prompt import generate_doc_name_by_convention_prompt
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

class DocumentMetadataExtractor:
    def __init__(self, gemini_client):
        self.gemini = gemini_client

    def extract_field_values(
        self,
        document: Union[str, Path],
        prompt: str,
        schema_type: Optional[Type] = None
    ) -> Dict[str, str]:
        """
        Extract fields from a document file or plain text.

        Args:
            document: Either a text string or a Path to a file.
            prompt: Prompt to send to Gemini.
            schema_type: Optional Pydantic model type for output validation.

        Returns:
            Dict of extracted field values.
        """
        try:
            if isinstance(document, str):
                logger.debug("Processing plain text input for Gemini")
                response = self.gemini.generate_from_text(
                    prompt=prompt,
                    document_text=document,
                    schema_type=schema_type
                )
            elif isinstance(document, Path):
                if not document.exists():
                    logger.error(f"File not found: {document}")
                    return {}
                logger.debug(f"Processing file input for Gemini: {document}")
                response = self.gemini.generate_from_file(
                    file_path=str(document),
                    prompt=prompt,
                    schema_type=schema_type
                )
            else:
                raise ValueError("Unsupported input type: must be str or Path")

            # --- Process the response ---
            if schema_type and hasattr(response, "dict"):
                return response.dict()

            if isinstance(response, dict):
                return response

            if isinstance(response, list) and all(hasattr(x, "dict") for x in response):
                return {k: v for item in response for k, v in item.dict().items()}

            if isinstance(response, str):
                cleaned = re.sub(r"^```json|```$", "", response.strip(), flags=re.IGNORECASE).strip()
                parsed = json.loads(cleaned)

                if schema_type:
                    return schema_type(**parsed).dict()
                if isinstance(parsed, dict):
                    return parsed
                if isinstance(parsed, list):
                    return {k: v for item in parsed if isinstance(item, dict) for k, v in item.items()}

            logger.warning("Unexpected Gemini response type")
            return {}

        except Exception as e:
            logger.error("Field extraction failed", exc_info=True)
            return {}


    # # TODO move to a separate class
    # def generate_document_name(
    #     self,
    #     document_bytes: bytes,
    #     field_values: Dict[str, str],
    #     naming_template: str
    # ) -> str:
    #     try:
    #         field_values_list = [f"{k}: {v}" for k, v in field_values.items() if v]
    #         prompt = generate_doc_name_by_convention_prompt(field_values_list, naming_template)
    #
    #         file_type, extension = self._detect_file_type(document_bytes)
    #         file_path = self._create_temp_file(document_bytes, extension)
    #
    #         try:
    #             doc_name = self.gemini.generate_from_file(
    #                 file_path=str(file_path),
    #                 prompt=prompt
    #             )
    #
    #             if isinstance(doc_name, str):
    #                 cleaned = doc_name.strip().strip('"').strip("'")
    #                 logger.info(f"Generated document name: {cleaned}")
    #                 return cleaned
    #
    #             logger.warning(f"Unexpected response type: {type(doc_name)}")
    #             return "Unknown Document"
    #
    #         finally:
    #             self._clean_temp_file(file_path)
    #
    #     except Exception as e:
    #         logger.error("Document name generation failed", exc_info=True)
    #         return "Unknown Document"
