from pathlib import Path
from typing import Dict
from src.filling_service.prompts.name_generation_prompt import generate_doc_name_by_convention_prompt
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

class DocumentNameGenerator:
    def __init__(self, gemini_client):
        self.gemini = gemini_client

    def generate_name(
        self,
        file_path: Path,
        field_values: Dict[str, str],
        naming_template: str
    ) -> str:
        """
        Generates a document name based on field values and a naming template.

        Args:
            file_path (Path): Path to the document file (e.g., PDF, DOCX).
            field_values (Dict[str, str]): Extracted field values.
            naming_template (str): A template like "ClientName - CaseNumber".

        Returns:
            str: The generated document name.
        """
        try:
            if not file_path.exists():
                logger.error(f"File not found for naming: {file_path}")
                return "Unknown Document"

            # Format field values into prompt list
            field_values_list = [f"{k}: {v}" for k, v in field_values.items() if v]
            prompt = generate_doc_name_by_convention_prompt(field_values_list, naming_template)

            logger.debug(f"[NAMING] Sending prompt to Gemini: {prompt}")
            doc_name = self.gemini.generate_from_file(file_path=str(file_path), prompt=prompt)

            if isinstance(doc_name, str):
                cleaned = doc_name.strip().strip('"').strip("'")
                logger.info(f"Generated document name: {cleaned}")
                return cleaned

            logger.warning(f"Unexpected Gemini response type: {type(doc_name)}")
            return "Unknown Document"

        except Exception as e:
            logger.error("Document name generation failed", exc_info=True)
            return "Unknown Document"
