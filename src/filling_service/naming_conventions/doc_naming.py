from src.filling_service.config import name_generator
from pathlib import Path
from src.shared.service_registry import doc_processor
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

def name_generation(
    doc_id: int,
    extracted_field_values: dict,
    preview_path: Path,
    doc_type_id: int,
) -> None:
    """
    Generates and stores a document name based on extracted field values and naming convention template.

    Args:
        doc_id (int): ID of the document.
        extracted_field_values (dict): Extracted metadata fields used for naming.
        preview_path (Path): Path to the document used for name generation.
        doc_type_id (int): The document type identifier.
    """
    service_id = 4
    logger.info(f"[NAMING] Starting document name generation for document ID: {doc_id}")
    doc_processor.log_document_service_status(doc_id, service_id=service_id, status_id=2)  # Status: Processing

    try:
        # 1. Get the naming template for this doc type and destination
        naming_template = doc_processor.get_naming_template_by_document_type(
            document_type_id=doc_type_id,
        )

        if not naming_template:
            logger.warning(
                f"[NAMING] No naming template found for document_type_id={doc_type_id} ")
            return

        if not extracted_field_values:
            logger.warning(f"[NAMING] No extracted values for doc_id={doc_id}, skipping.")
            return

        logger.info(f"[NAMING] Using naming template: {naming_template}")

        # 2. Generate the name using the external name generator class
        doc_name = name_generator.generate_name(preview_path, extracted_field_values, naming_template)

        if doc_name:
            doc_processor.log_document_name(doc_id, doc_name)
            doc_processor.log_document_service_status(doc_id, service_id=service_id, status_id=3)  # Status: Success
            logger.info(f"[NAMING] Successfully stored document name: {doc_name}")
        else:
            logger.warning(f"[NAMING] Failed to generate name for doc_id={doc_id}")
            doc_processor.mark_document_for_manual_review(doc_id)
            doc_processor.log_document_service_status(doc_id, service_id=service_id, status_id=4)  # Status: Failed

    except Exception as naming_error:
        logger.error(f"[NAMING] Error during name generation for doc_id={doc_id}: {naming_error}", exc_info=True)
        doc_processor.log_document_service_status(doc_id, service_id=service_id, status_id=4)


# Sample Usage
# with open("/Users/<USER>/Desktop/fileflow/fileflow.document-classifier/tests_classifier_datasets/non_court_docs/non_court_documents_dataset/1p UM_UIM LOR - State Farm - Angelica de Lacerda.pdf", "rb") as f:
#     preview_bytes = f.read()
# extracted_fields = {"Defendant Name": "Maria Guzman, Francisco Guzman, and John Doe"}
# document_name = name_generation(doc_id=268, extracted_field_values=extracted_fields, preview_bytes=preview_bytes, doc_type_id=101)
# print(document_name)
