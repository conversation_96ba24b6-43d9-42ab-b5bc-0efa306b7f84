from src.filling_service.fields_extraction.document_metadata_extractor import DocumentMetadataExtractor
from src.filling_service.naming_conventions.name_generator_engine import DocumentNameGenerator
from src.filling_service.project_matching_refactor.core.matching_engine import DocumentProjectMatcher
from src.shared.service_registry import gemini_client, db
from src.shared.utils.file_utils import FileHandler

extractor =  DocumentMetadataExtractor(gemini_client)
doc_case_matcher = DocumentProjectMatcher(db)
file_handler = FileHandler()
name_generator = DocumentNameGenerator(gemini_client)