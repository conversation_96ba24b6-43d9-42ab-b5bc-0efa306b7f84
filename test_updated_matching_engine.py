#!/usr/bin/env python3
"""
Test script to demonstrate the updated DocumentProjectMatcher
that follows the new workflow and returns detailed field matches.
"""

from src.filling_service.project_matching_refactor.core.matching_engine import DocumentProjectMatcher
from src.shared.service_registry import db

def test_case_number_matching():
    """Test case number matching (Step 1)."""
    
    print("=" * 80)
    print("TEST 1: CASE NUMBER MATCHING")
    print("=" * 80)
    
    doc_matcher = DocumentProjectMatcher(db)
    
    # Test data with case number
    unpacked_values = {
        "case_number": "**********-25",  # This should match directly
        "client_name": "<PERSON>",
        "incident_date": "2025-02-21T00:00:00.0000000",
        "incident_type": "motor vehicle accident",
        "lead_attorney": "<PERSON>",
        "project_email_address": "<EMAIL>"
    }
    
    company_data_destination_id = 6
    destination_source_id = 1
    
    print(f"Input values: {unpacked_values}")
    print()
    
    # Call the updated match_project method
    project_id_or_list, manual_review, matched_fields = doc_matcher.match_project(
        company_data_destination_id=company_data_destination_id,
        destination_source_id=destination_source_id,
        unpacked_values=unpacked_values
    )
    
    print("\n" + "=" * 60)
    print("CASE NUMBER MATCHING RESULTS:")
    print("=" * 60)
    print(f"Project ID(s): {project_id_or_list}")
    print(f"Manual review needed: {manual_review}")
    print(f"Matched fields:")
    
    if matched_fields:
        for field_name, (extracted_val, db_val) in matched_fields.items():
            print(f"  {field_name}: '{extracted_val}' ↔ '{db_val}'")
    else:
        print("  No matched fields")
    
    return project_id_or_list, manual_review, matched_fields

def test_client_name_single_match():
    """Test client name matching with single strong match (Step 2 + 3a)."""
    
    print("\n" + "=" * 80)
    print("TEST 2: CLIENT NAME SINGLE MATCH + SECONDARY VERIFICATION")
    print("=" * 80)
    
    doc_matcher = DocumentProjectMatcher(db)
    
    # Test data without case number, with specific client name
    unpacked_values = {
        "client_name": "Hernandez Martinez",  # Should find single strong match
        "incident_date": "2025-02-21T00:00:00.0000000",
        "incident_type": "motor vehicle accident",
        "lead_attorney": "Jane Doe",
        "project_email_address": "<EMAIL>"
    }
    
    company_data_destination_id = 6
    destination_source_id = 1
    
    print(f"Input values: {unpacked_values}")
    print()
    
    project_id_or_list, manual_review, matched_fields = doc_matcher.match_project(
        company_data_destination_id=company_data_destination_id,
        destination_source_id=destination_source_id,
        unpacked_values=unpacked_values
    )
    
    print("\n" + "=" * 60)
    print("SINGLE CLIENT NAME MATCH RESULTS:")
    print("=" * 60)
    print(f"Project ID(s): {project_id_or_list}")
    print(f"Manual review needed: {manual_review}")
    print(f"Matched fields:")
    
    if matched_fields:
        for field_name, (extracted_val, db_val) in matched_fields.items():
            print(f"  {field_name}: '{extracted_val}' ↔ '{db_val}'")
    else:
        print("  No matched fields")
    
    return project_id_or_list, manual_review, matched_fields

def test_client_name_multiple_matches():
    """Test client name matching with multiple candidates (Step 2 + 3b)."""
    
    print("\n" + "=" * 80)
    print("TEST 3: CLIENT NAME MULTIPLE MATCHES + SECONDARY EVALUATION")
    print("=" * 80)
    
    doc_matcher = DocumentProjectMatcher(db)
    
    # Test data with common name that might match multiple projects
    unpacked_values = {
        "client_name": "Smith",  # Common name, likely multiple matches
        "incident_date": "2025-02-21T00:00:00.0000000",
        "incident_type": "motor vehicle accident",
        "lead_attorney": "Jane Doe",
        "project_email_address": "<EMAIL>"
    }
    
    company_data_destination_id = 6
    destination_source_id = 1
    
    print(f"Input values: {unpacked_values}")
    print()
    
    project_id_or_list, manual_review, matched_fields = doc_matcher.match_project(
        company_data_destination_id=company_data_destination_id,
        destination_source_id=destination_source_id,
        unpacked_values=unpacked_values
    )
    
    print("\n" + "=" * 60)
    print("MULTIPLE CLIENT NAME MATCHES RESULTS:")
    print("=" * 60)
    print(f"Project ID(s): {project_id_or_list}")
    print(f"Manual review needed: {manual_review}")
    print(f"Matched fields:")
    
    if matched_fields:
        for field_name, (extracted_val, db_val) in matched_fields.items():
            print(f"  {field_name}: '{extracted_val}' ↔ '{db_val}'")
    else:
        print("  No matched fields")
    
    return project_id_or_list, manual_review, matched_fields

def test_no_matches():
    """Test scenario with no matches."""
    
    print("\n" + "=" * 80)
    print("TEST 4: NO MATCHES SCENARIO")
    print("=" * 80)
    
    doc_matcher = DocumentProjectMatcher(db)
    
    # Test data that shouldn't match anything
    unpacked_values = {
        "case_number": "NONEXISTENT-CASE-123",
        "client_name": "Nonexistent Client Name XYZ",
        "incident_date": "2025-02-21T00:00:00.0000000",
        "incident_type": "motor vehicle accident",
        "lead_attorney": "Jane Doe"
    }
    
    company_data_destination_id = 6
    destination_source_id = 1
    
    print(f"Input values: {unpacked_values}")
    print()
    
    project_id_or_list, manual_review, matched_fields = doc_matcher.match_project(
        company_data_destination_id=company_data_destination_id,
        destination_source_id=destination_source_id,
        unpacked_values=unpacked_values
    )
    
    print("\n" + "=" * 60)
    print("NO MATCHES RESULTS:")
    print("=" * 60)
    print(f"Project ID(s): {project_id_or_list}")
    print(f"Manual review needed: {manual_review}")
    print(f"Matched fields:")
    
    if matched_fields:
        for field_name, (extracted_val, db_val) in matched_fields.items():
            print(f"  {field_name}: '{extracted_val}' ↔ '{db_val}'")
    else:
        print("  No matched fields")
    
    return project_id_or_list, manual_review, matched_fields

if __name__ == "__main__":
    try:
        print("🚀 TESTING UPDATED DOCUMENT PROJECT MATCHER")
        print("Following the new workflow:")
        print("1. Try case number matching first")
        print("2. Try client name matching")
        print("3a. For single strong client match, verify with secondary fields")
        print("3b. For multiple client matches, evaluate secondary fields among candidates")
        print("Returns: project_id(s), manual_review_flag, matched_fields_dict")
        
        # Run all tests
        test_case_number_matching()
        test_client_name_single_match()
        test_client_name_multiple_matches()
        test_no_matches()
        
        print("\n" + "=" * 80)
        print("✅ ALL TESTS COMPLETED")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
